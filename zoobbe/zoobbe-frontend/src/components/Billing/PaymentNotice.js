import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';
import './scss/PaymentNotice.scss';
import { config } from '../../config';

/**
 * PaymentNotice component displays notifications for billing-related actions
 * - For workspace admins: Shows notice to pay for pending seats
 * - For view-only members: Shows notice that admin needs to pay for their seat
 */
const PaymentNotice = ({ workspaceId, boardId }) => {
  const [billingStatus, setBillingStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showNotice, setShowNotice] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  const [currentWorkspaceId, setCurrentWorkspaceId] = useState(workspaceId);

  const { user } = useSelector(state => state.user);
  const { workspaces } = useSelector(state => state.workspaces);

  const boards = useSelector(state => state.boards.boards);

  // If we're on a board page, find the workspace ID from the board
  useEffect(() => {
    if (boardId && !workspaceId) {
      const board = boards.find(b => b.shortId === boardId);
      if (board?.workspace?.shortId) {
        setCurrentWorkspaceId(board.workspace.shortId);
      }
    }
  }, [boardId, workspaceId, boards]);

  // Get the current workspace
  const workspace = workspaces.find(w => w.shortId === currentWorkspaceId);

  // Check if user is admin
  const isAdmin = workspace?.members?.some(
    member => member.user === user?.user?._id && (member.role === 'admin' || workspace.ownerId === user?.user?._id)
  );

  // Check if user has view-only access
  const isViewOnly = workspace?.members?.some(
    member => member.user === user?.user?._id && member.accessLevel === 'view-only'
  );

  useEffect(() => {
    const fetchBillingStatus = async () => {
      try {
        setIsLoading(true);
        const res = await fetch(config.API_URI + `/api/stripe/billing-status/${currentWorkspaceId}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
        });

        if (res.ok) {
          const data = await res.json();
          setBillingStatus(data);

          // Show notice if:
          // 1. User is admin and there are pending seats
          // 2. User has view-only access
          if ((isAdmin && data.pendingSeats > 0) || isViewOnly) {
            setShowNotice(true);
          }
        }
      } catch (error) {
        console.error('Error fetching billing status:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Check if we should show the notice based on localStorage
    const checkDismissalStatus = () => {
      const dismissedTime = localStorage.getItem(`payment-notice-dismissed-${currentWorkspaceId}`);
      if (dismissedTime) {
        const dismissedAt = parseInt(dismissedTime);
        const now = Date.now();
        // If it's been less than 24 hours since dismissal, don't show
        if (now - dismissedAt < 24 * 60 * 60 * 1000) {
          setIsDismissed(true);
        }
      }
    };

    if (currentWorkspaceId && (isAdmin || isViewOnly)) {
      checkDismissalStatus();
      fetchBillingStatus();
    }
  }, [currentWorkspaceId, isAdmin, isViewOnly]);

  const handleDismiss = () => {
    setIsDismissed(true);
    // Store dismissal in localStorage to prevent showing again for some time
    localStorage.setItem(`payment-notice-dismissed-${currentWorkspaceId}`, Date.now().toString());
  };

  if (isLoading || !showNotice || isDismissed) {
    return null;
  }

  return (
    <div className="payment-notice-container">
      {isAdmin && billingStatus?.pendingSeats > 0 ? (
        <div className="payment-notice admin-notice">
          <div className="notice-content">
            <div className="notice-icon">
              <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
                <path d="M480-280q17 0 28.5-11.5T520-320q0-17-11.5-28.5T480-360q-17 0-28.5 11.5T440-320q0 17 11.5 28.5T480-280Zm-40-160h80v-240h-80v240Zm40 360q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z" fill="#ff9800" />
              </svg>
            </div>
            <div className="notice-text">
              <p>
                <strong>Action Required:</strong> You have {billingStatus.pendingSeats} pending seat{billingStatus.pendingSeats > 1 ? 's' : ''} that require payment.
                Members with pending seats currently have view-only access.
              </p>
            </div>
          </div>
          <div className="notice-actions">
            <Link to={`/w/${currentWorkspaceId}/billing`} className="pay-button">
              Pay Now
            </Link>
            <button className="dismiss-button" onClick={handleDismiss}>
              Dismiss
            </button>
          </div>
        </div>
      ) : isViewOnly ? (
        <div className="payment-notice member-notice">
          <div className="notice-content">
            <div className="notice-icon">
              <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
                <path d="M480-280q17 0 28.5-11.5T520-320q0-17-11.5-28.5T480-360q-17 0-28.5 11.5T440-320q0 17 11.5 28.5T480-280Zm-40-160h80v-240h-80v240Zm40 360q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z" fill="#ff9800" />
              </svg>
            </div>
            <div className="notice-text">
              <p>
                <strong>View-Only Access:</strong> You currently have view-only access to this workspace.
                Please contact the workspace admin to upgrade your access.
              </p>
            </div>
          </div>
          <div className="notice-actions">
            <button className="dismiss-button" onClick={handleDismiss}>
              Dismiss
            </button>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default PaymentNotice;
