import { useState } from 'react';
import type { FormEvent } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { RiLockLine, RiMailLine, RiErrorWarningLine } from 'react-icons/ri';
import Input from '../components/ui/Input';
import Button from '../components/ui/Button';

interface LocationState {
  from?: {
    pathname: string;
  };
}

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const locationState = location.state as LocationState;
  const from = locationState?.from?.pathname || '/dashboard';

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await login(email, password);
      navigate(from, { replace: true });
    } catch (err: any) {
      console.error('Login error:', err);

      // Extract detailed error message
      let errorMessage = 'Invalid credentials. Please try again.';

      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        errorMessage = err.response.data?.message || errorMessage;
      } else if (err.request) {
        // The request was made but no response was received
        errorMessage = 'No response from server. Please check your connection.';
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = err.message || errorMessage;
      }

      setError(errorMessage);
      setIsLoading(false);
    }
  };

  return (
    <div id="login-container" className="login-container auth-container">
      {error && (
        <div id="login-error" className="mb-4 rounded-md bg-red-50 dark:bg-red-900/20 p-4 error-alert">
          <div className="flex error-content">
            <div className="flex-shrink-0 error-icon-container">
              <RiErrorWarningLine className="h-5 w-5 text-red-400 dark:text-red-300 error-icon" aria-hidden="true" />
            </div>
            <div className="ml-3 error-message-container">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-300 error-title">Authentication Error</h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-200 error-details">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <form id="login-form" onSubmit={handleSubmit} className="space-y-6 auth-form">
        <Input
          id="email"
          name="email"
          type="email"
          label="Email address"
          autoComplete="email"
          required
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          leftIcon={<RiMailLine />}
          error={error ? " " : undefined}
          placeholder="<EMAIL>"
          fullWidth
        />

        <Input
          id="password"
          name="password"
          type="password"
          label="Password"
          autoComplete="current-password"
          required
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          leftIcon={<RiLockLine />}
          error={error ? " " : undefined}
          placeholder="••••••••"
          fullWidth
        />

        <div id="login-options" className="flex items-center justify-between form-options">
          <div className="flex items-center remember-me-container">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-slate-600 dark:bg-slate-700 rounded checkbox-input"
            />
            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900 dark:text-gray-300 checkbox-label">
              Remember me
            </label>
          </div>

          <div className="text-sm forgot-password-container">
            <a href="#" className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 forgot-password-link">
              Forgot your password?
            </a>
          </div>
        </div>

        <div className="form-submit">
          <Button
            id="login-submit"
            type="submit"
            disabled={isLoading}
            isLoading={isLoading}
            variant="primary"
            fullWidth
          >
            {isLoading ? 'Signing in...' : 'Sign in'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default Login;
